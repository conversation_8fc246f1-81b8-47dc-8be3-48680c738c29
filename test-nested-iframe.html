<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>多层iframe嵌套测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f0f0f0;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        .iframe-container {
            border: 2px solid #333;
            margin: 20px 0;
            padding: 10px;
            background-color: white;
        }
        .iframe-container h3 {
            margin-top: 0;
            color: #333;
        }
        iframe {
            width: 100%;
            height: 400px;
            border: 1px solid #ccc;
        }
        .test-content {
            padding: 20px;
            background-color: #e8f4f8;
            margin: 10px 0;
            border-radius: 5px;
        }
        .selectable-text {
            background-color: #fff3cd;
            padding: 10px;
            margin: 10px 0;
            border-left: 4px solid #ffc107;
            line-height: 1.6;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>多层iframe嵌套测试页面</h1>
        <p>这个页面用于测试浏览器扩展在多层iframe嵌套环境下的划词和便签拖拽功能。</p>
        
        <div class="test-content">
            <h2>主页面内容</h2>
            <div class="selectable-text">
                这是主页面的可选择文本。请尝试选择这段文字来测试划词功能。
                Lorem ipsum dolor sit amet, consectetur adipiscing elit. 
                Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.
            </div>
        </div>

        <div class="iframe-container">
            <h3>第一层iframe</h3>
            <iframe src="data:text/html;charset=utf-8,
                <!DOCTYPE html>
                <html>
                <head>
                    <meta charset='UTF-8'>
                    <style>
                        body { font-family: Arial, sans-serif; padding: 20px; background-color: #f8f9fa; }
                        .content { background-color: #d4edda; padding: 15px; margin: 10px 0; border-radius: 5px; }
                        .selectable { background-color: #fff3cd; padding: 10px; margin: 10px 0; border-left: 4px solid #ffc107; }
                        iframe { width: 100%; height: 300px; border: 1px solid #ccc; margin: 10px 0; }
                    </style>
                </head>
                <body>
                    <h2>第一层iframe内容</h2>
                    <div class='content'>
                        <p>这是第一层iframe中的内容。</p>
                        <div class='selectable'>
                            请选择这段文字测试第一层iframe的划词功能。
                            Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris.
                        </div>
                    </div>
                    
                    <h3>嵌套的第二层iframe</h3>
                    <iframe src='data:text/html;charset=utf-8,
                        <!DOCTYPE html>
                        <html>
                        <head>
                            <meta charset=\"UTF-8\">
                            <style>
                                body { font-family: Arial, sans-serif; padding: 15px; background-color: #e2e3e5; }
                                .content { background-color: #d1ecf1; padding: 10px; margin: 10px 0; border-radius: 5px; }
                                .selectable { background-color: #f8d7da; padding: 10px; margin: 10px 0; border-left: 4px solid #dc3545; }
                                iframe { width: 100%; height: 200px; border: 1px solid #ccc; margin: 10px 0; }
                            </style>
                        </head>
                        <body>
                            <h3>第二层iframe内容</h3>
                            <div class=\"content\">
                                <p>这是第二层嵌套iframe中的内容。</p>
                                <div class=\"selectable\">
                                    请选择这段文字测试第二层iframe的划词功能。
                                    Duis aute irure dolor in reprehenderit in voluptate velit esse cillum.
                                </div>
                            </div>
                            
                            <h4>第三层iframe</h4>
                            <iframe src=\"data:text/html;charset=utf-8,
                                <!DOCTYPE html>
                                <html>
                                <head>
                                    <meta charset=\\\"UTF-8\\\">
                                    <style>
                                        body { font-family: Arial, sans-serif; padding: 10px; background-color: #f1f3f4; }
                                        .content { background-color: #e1f5fe; padding: 10px; margin: 5px 0; border-radius: 3px; }
                                        .selectable { background-color: #ffecb3; padding: 8px; margin: 5px 0; border-left: 3px solid #ff9800; }
                                    </style>
                                </head>
                                <body>
                                    <h4>第三层iframe内容</h4>
                                    <div class=\\\"content\\\">
                                        <p>这是第三层嵌套iframe中的内容。</p>
                                        <div class=\\\"selectable\\\">
                                            请选择这段文字测试第三层iframe的划词功能。
                                            Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia.
                                        </div>
                                    </div>
                                </body>
                                </html>
                            \"></iframe>
                        </body>
                        </html>
                    '></iframe>
                </body>
                </html>
            "></iframe>
        </div>

        <div class="iframe-container">
            <h3>另一个独立的iframe</h3>
            <iframe src="data:text/html;charset=utf-8,
                <!DOCTYPE html>
                <html>
                <head>
                    <meta charset='UTF-8'>
                    <style>
                        body { font-family: Arial, sans-serif; padding: 20px; background-color: #ffeaa7; }
                        .content { background-color: #fab1a0; padding: 15px; margin: 10px 0; border-radius: 5px; color: white; }
                        .selectable { background-color: #fd79a8; padding: 10px; margin: 10px 0; border-left: 4px solid #e84393; color: white; }
                    </style>
                </head>
                <body>
                    <h2>独立iframe内容</h2>
                    <div class='content'>
                        <p>这是一个独立的iframe，用于测试多个iframe并存的情况。</p>
                        <div class='selectable'>
                            请选择这段文字测试独立iframe的划词功能。
                            Sed ut perspiciatis unde omnis iste natus error sit voluptatem accusantium.
                        </div>
                    </div>
                </body>
                </html>
            "></iframe>
        </div>

        <div class="test-content">
            <h2>测试说明</h2>
            <ul>
                <li><strong>划词测试</strong>：在不同层级的iframe中选择文字，观察是否能正常触发划词功能</li>
                <li><strong>便签拖拽测试</strong>：创建便签后，尝试拖拽便签，观察在iframe区域是否能正常拖拽</li>
                <li><strong>多层嵌套</strong>：特别关注第二层和第三层iframe中的功能是否正常</li>
                <li><strong>坐标计算</strong>：检查划词弹窗的位置是否准确对应选择的文字位置</li>
            </ul>
        </div>
    </div>

    <script>
        // 添加一些调试信息
        console.log('测试页面加载完成');
        console.log('iframe数量:', document.querySelectorAll('iframe').length);
        
        // 监听消息，用于调试
        window.addEventListener('message', (e) => {
            console.log('主页面收到消息:', e.data);
        });
    </script>
</body>
</html>
