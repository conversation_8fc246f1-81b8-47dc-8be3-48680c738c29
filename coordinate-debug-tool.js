// 坐标调试工具：帮助诊断多层iframe中的坐标计算问题
(function() {
    'use strict';
    
    let debugPanel = null;
    let isDebugging = false;
    
    // 创建调试面板
    function createDebugPanel() {
        if (debugPanel) return debugPanel;
        
        debugPanel = document.createElement('div');
        debugPanel.id = 'coordinate-debug-panel';
        debugPanel.style.cssText = `
            position: fixed;
            top: 50px;
            left: 10px;
            width: 350px;
            max-height: 500px;
            background: rgba(0, 0, 0, 0.95);
            color: #00ff00;
            padding: 15px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            font-size: 11px;
            z-index: 2147483647;
            overflow-y: auto;
            border: 2px solid #00ff00;
        `;
        
        const title = document.createElement('h3');
        title.textContent = '🎯 坐标调试工具';
        title.style.cssText = 'margin: 0 0 10px 0; color: #ffff00; text-align: center;';
        debugPanel.appendChild(title);
        
        const toggleBtn = document.createElement('button');
        toggleBtn.textContent = isDebugging ? '停止调试' : '开始调试';
        toggleBtn.style.cssText = `
            background: ${isDebugging ? '#ff4444' : '#44ff44'};
            color: black;
            border: none;
            padding: 5px 10px;
            border-radius: 4px;
            cursor: pointer;
            margin-bottom: 10px;
            width: 100%;
        `;
        toggleBtn.onclick = toggleDebugging;
        debugPanel.appendChild(toggleBtn);
        
        const content = document.createElement('div');
        content.id = 'coordinate-debug-content';
        debugPanel.appendChild(content);
        
        document.body.appendChild(debugPanel);
        return debugPanel;
    }
    
    // 切换调试状态
    function toggleDebugging() {
        isDebugging = !isDebugging;
        const btn = debugPanel.querySelector('button');
        btn.textContent = isDebugging ? '停止调试' : '开始调试';
        btn.style.background = isDebugging ? '#ff4444' : '#44ff44';
        
        if (isDebugging) {
            startDebugging();
        } else {
            stopDebugging();
        }
    }
    
    // 开始调试
    function startDebugging() {
        document.addEventListener('mouseup', debugMouseUp, true);
        document.addEventListener('mousemove', debugMouseMove, true);
        updateDebugInfo('🟢 调试已启动 - 请选择文字或移动鼠标');
    }
    
    // 停止调试
    function stopDebugging() {
        document.removeEventListener('mouseup', debugMouseUp, true);
        document.removeEventListener('mousemove', debugMouseMove, true);
        updateDebugInfo('🔴 调试已停止');
    }
    
    // 调试鼠标抬起事件
    function debugMouseUp(e) {
        const selection = window.getSelection();
        if (!selection || selection.isCollapsed) return;
        
        const text = selection.toString().trim();
        if (!text) return;
        
        const range = selection.getRangeAt(0);
        const rect = range.getBoundingClientRect();
        
        // 获取iframe信息
        const iframeInfo = getIframeInfo();
        
        // 计算各种坐标
        const coordinates = calculateCoordinates(rect, iframeInfo);
        
        displayCoordinateInfo(text, rect, iframeInfo, coordinates);
    }
    
    // 调试鼠标移动事件
    function debugMouseMove(e) {
        if (!isDebugging) return;
        
        const iframeInfo = getIframeInfo();
        const mouseInfo = {
            clientX: e.clientX,
            clientY: e.clientY,
            pageX: e.pageX,
            pageY: e.pageY,
            screenX: e.screenX,
            screenY: e.screenY
        };
        
        updateMouseInfo(mouseInfo, iframeInfo);
    }
    
    // 获取iframe信息
    function getIframeInfo() {
        const info = {
            isIframe: window !== window.top,
            level: 0,
            path: [],
            cumulativeOffset: { top: 0, left: 0 },
            scrollOffset: {
                top: window.pageYOffset || document.documentElement.scrollTop || document.body.scrollTop || 0,
                left: window.pageXOffset || document.documentElement.scrollLeft || document.body.scrollLeft || 0
            }
        };
        
        if (!info.isIframe) return info;
        
        // 计算iframe层级和路径
        let currentWindow = window;
        while (currentWindow !== window.top && currentWindow.parent !== currentWindow) {
            info.level++;
            try {
                const parentDoc = currentWindow.parent.document;
                const iframes = parentDoc.querySelectorAll('iframe');
                
                for (let i = 0; i < iframes.length; i++) {
                    if (iframes[i].contentWindow === currentWindow) {
                        const rect = iframes[i].getBoundingClientRect();
                        const parentScrollTop = currentWindow.parent.pageYOffset || parentDoc.documentElement.scrollTop || parentDoc.body.scrollTop || 0;
                        const parentScrollLeft = currentWindow.parent.pageXOffset || parentDoc.documentElement.scrollLeft || parentDoc.body.scrollLeft || 0;
                        
                        info.cumulativeOffset.top += rect.top + parentScrollTop;
                        info.cumulativeOffset.left += rect.left + parentScrollLeft;
                        info.path.unshift(i);
                        break;
                    }
                }
            } catch (e) {
                console.warn('无法访问父窗口:', e);
                break;
            }
            currentWindow = currentWindow.parent;
        }
        
        return info;
    }
    
    // 计算各种坐标
    function calculateCoordinates(rect, iframeInfo) {
        return {
            // 原始坐标（相对于当前窗口视口）
            viewport: {
                top: rect.top,
                left: rect.left,
                width: rect.width,
                height: rect.height
            },
            // 加上当前窗口滚动偏移
            withScroll: {
                top: rect.top + iframeInfo.scrollOffset.top,
                left: rect.left + iframeInfo.scrollOffset.left,
                width: rect.width,
                height: rect.height
            },
            // 加上累积iframe偏移（最终坐标）
            global: {
                top: rect.top + iframeInfo.scrollOffset.top + iframeInfo.cumulativeOffset.top,
                left: rect.left + iframeInfo.scrollOffset.left + iframeInfo.cumulativeOffset.left,
                width: rect.width,
                height: rect.height
            }
        };
    }
    
    // 显示坐标信息
    function displayCoordinateInfo(text, rect, iframeInfo, coordinates) {
        const info = `
📍 选择文字: "${text.substring(0, 30)}${text.length > 30 ? '...' : ''}"

🏠 窗口信息:
  • 是否iframe: ${iframeInfo.isIframe ? 'YES' : 'NO'}
  • iframe层级: ${iframeInfo.level}
  • iframe路径: [${iframeInfo.path.join(', ')}]

📏 坐标信息:
  • 视口坐标: (${coordinates.viewport.left.toFixed(1)}, ${coordinates.viewport.top.toFixed(1)})
  • 含滚动偏移: (${coordinates.withScroll.left.toFixed(1)}, ${coordinates.withScroll.top.toFixed(1)})
  • 全局坐标: (${coordinates.global.left.toFixed(1)}, ${coordinates.global.top.toFixed(1)})

🔄 偏移量:
  • 当前滚动: (${iframeInfo.scrollOffset.left}, ${iframeInfo.scrollOffset.top})
  • 累积iframe: (${iframeInfo.cumulativeOffset.left.toFixed(1)}, ${iframeInfo.cumulativeOffset.top.toFixed(1)})

📐 尺寸: ${coordinates.viewport.width.toFixed(1)} × ${coordinates.viewport.height.toFixed(1)}
        `;
        
        updateDebugInfo(info);
    }
    
    // 更新鼠标信息
    function updateMouseInfo(mouseInfo, iframeInfo) {
        const info = `
🖱️ 鼠标位置 (Level ${iframeInfo.level}):
  • Client: (${mouseInfo.clientX}, ${mouseInfo.clientY})
  • Page: (${mouseInfo.pageX}, ${mouseInfo.pageY})
  • Screen: (${mouseInfo.screenX}, ${mouseInfo.screenY})
  • 全局估算: (${mouseInfo.clientX + iframeInfo.cumulativeOffset.left}, ${mouseInfo.clientY + iframeInfo.cumulativeOffset.top})
        `;
        
        updateDebugInfo(info);
    }
    
    // 更新调试信息
    function updateDebugInfo(info) {
        const content = document.getElementById('coordinate-debug-content');
        if (content) {
            content.innerHTML = `<pre style="margin: 0; white-space: pre-wrap;">${info}</pre>`;
        }
    }
    
    // 监听消息
    window.addEventListener('message', (e) => {
        if (e.data.type === 'iframe-selection' && isDebugging) {
            const messageInfo = `
📨 收到iframe消息:
  • 类型: ${e.data.type}
  • 文字: "${e.data.text.substring(0, 20)}..."
  • 原始坐标: (${e.data.rect.left}, ${e.data.rect.top})
  • 累积偏移: ${e.data.cumulativeOffset ? `(${e.data.cumulativeOffset.left}, ${e.data.cumulativeOffset.top})` : '无'}
  • iframe路径: [${e.data.iframePath ? e.data.iframePath.join(', ') : '无'}]
            `;
            updateDebugInfo(messageInfo);
        }
    });
    
    // 初始化
    function init() {
        createDebugPanel();
        
        // 添加快捷键 Ctrl+Shift+D
        document.addEventListener('keydown', (e) => {
            if (e.ctrlKey && e.shiftKey && e.key === 'D') {
                e.preventDefault();
                if (!debugPanel) {
                    createDebugPanel();
                } else {
                    debugPanel.style.display = debugPanel.style.display === 'none' ? 'block' : 'none';
                }
            }
        });
        
        console.log('🎯 坐标调试工具已加载 - 按 Ctrl+Shift+D 显示/隐藏面板');
    }
    
    // 等待DOM加载完成
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', init);
    } else {
        init();
    }
    
    // 导出到全局
    window.coordinateDebugTool = {
        show: () => createDebugPanel(),
        toggle: toggleDebugging,
        getIframeInfo,
        calculateCoordinates
    };
    
})();
