// 测试iframe修复效果的脚本
(function() {
    'use strict';
    
    let testResults = [];
    
    // 创建测试结果面板
    function createTestPanel() {
        const panel = document.createElement('div');
        panel.id = 'iframe-test-panel';
        panel.style.cssText = `
            position: fixed;
            top: 10px;
            left: 50%;
            transform: translateX(-50%);
            width: 600px;
            max-height: 400px;
            background: rgba(255, 255, 255, 0.95);
            border: 2px solid #007bff;
            border-radius: 8px;
            padding: 15px;
            font-family: Arial, sans-serif;
            font-size: 14px;
            z-index: 2147483647;
            overflow-y: auto;
            box-shadow: 0 4px 20px rgba(0,0,0,0.3);
        `;
        
        const title = document.createElement('h3');
        title.textContent = '🧪 iframe功能测试结果';
        title.style.cssText = 'margin: 0 0 15px 0; color: #007bff; text-align: center;';
        panel.appendChild(title);
        
        const content = document.createElement('div');
        content.id = 'test-results-content';
        panel.appendChild(content);
        
        const closeBtn = document.createElement('button');
        closeBtn.textContent = '关闭';
        closeBtn.style.cssText = `
            position: absolute;
            top: 10px;
            right: 10px;
            background: #dc3545;
            color: white;
            border: none;
            padding: 5px 10px;
            border-radius: 4px;
            cursor: pointer;
        `;
        closeBtn.onclick = () => panel.remove();
        panel.appendChild(closeBtn);
        
        document.body.appendChild(panel);
        return content;
    }
    
    // 添加测试结果
    function addTestResult(test, status, details) {
        const result = {
            timestamp: new Date().toLocaleTimeString(),
            test,
            status,
            details
        };
        testResults.push(result);
        updateTestDisplay();
    }
    
    // 更新测试显示
    function updateTestDisplay() {
        const content = document.getElementById('test-results-content');
        if (!content) return;
        
        let html = '<div style="margin-bottom: 15px;"><strong>测试进度:</strong></div>';
        
        testResults.forEach(result => {
            const statusColor = result.status === 'PASS' ? '#28a745' : 
                               result.status === 'FAIL' ? '#dc3545' : '#ffc107';
            
            html += `
                <div style="margin: 8px 0; padding: 8px; border-left: 4px solid ${statusColor}; background: #f8f9fa;">
                    <div style="font-weight: bold; color: ${statusColor};">
                        [${result.timestamp}] ${result.test} - ${result.status}
                    </div>
                    <div style="margin-top: 4px; font-size: 12px; color: #666;">
                        ${result.details}
                    </div>
                </div>
            `;
        });
        
        content.innerHTML = html;
    }
    
    // 测试消息监听
    function testMessageListening() {
        addTestResult('消息监听', 'INFO', '开始监听iframe消息...');
        
        window.addEventListener('message', (e) => {
            if (e.data.type === 'iframe-selection') {
                const hasOffset = !!e.data.cumulativeOffset;
                const hasPath = !!e.data.iframePath;
                
                addTestResult(
                    '划词消息接收', 
                    hasOffset && hasPath ? 'PASS' : 'WARN',
                    `文字: "${e.data.text.substring(0, 20)}..." | 累积偏移: ${hasOffset ? '✓' : '✗'} | 路径: ${hasPath ? '✓' : '✗'}`
                );
                
                // 测试坐标计算
                if (e.data.cumulativeOffset) {
                    const finalX = e.data.rect.left + e.data.cumulativeOffset.left;
                    const finalY = e.data.rect.top + e.data.cumulativeOffset.top;
                    
                    addTestResult(
                        '坐标计算',
                        'PASS',
                        `最终坐标: (${finalX.toFixed(1)}, ${finalY.toFixed(1)})`
                    );
                }
            }
            
            if (e.data.type === 'iframe-contextmenu') {
                const hasOffset = !!e.data.cumulativeOffset;
                
                addTestResult(
                    '右键菜单消息',
                    hasOffset ? 'PASS' : 'WARN',
                    `位置: (${e.data.clientX}, ${e.data.clientY}) | 累积偏移: ${hasOffset ? '✓' : '✗'}`
                );
            }
            
            if (e.data.type === 'iframe-mousedown') {
                addTestResult('鼠标点击消息', 'PASS', '收到iframe鼠标点击消息');
            }
        });
    }
    
    // 测试iframe结构分析
    function testIframeStructure() {
        const iframes = document.querySelectorAll('iframe');
        addTestResult(
            'iframe结构分析',
            iframes.length > 0 ? 'PASS' : 'FAIL',
            `发现 ${iframes.length} 个iframe`
        );
        
        // 尝试访问iframe内容
        let accessibleCount = 0;
        iframes.forEach((iframe, index) => {
            try {
                const doc = iframe.contentDocument || iframe.contentWindow?.document;
                if (doc) {
                    accessibleCount++;
                    const nestedIframes = doc.querySelectorAll('iframe');
                    addTestResult(
                        `iframe[${index}]访问`,
                        'PASS',
                        `可访问，包含 ${nestedIframes.length} 个嵌套iframe`
                    );
                }
            } catch (e) {
                addTestResult(
                    `iframe[${index}]访问`,
                    'WARN',
                    '跨域限制，无法访问内容'
                );
            }
        });
        
        addTestResult(
            'iframe访问统计',
            accessibleCount > 0 ? 'PASS' : 'WARN',
            `${accessibleCount}/${iframes.length} 个iframe可访问`
        );
    }
    
    // 模拟测试事件
    function simulateTests() {
        setTimeout(() => {
            addTestResult('自动测试', 'INFO', '开始模拟测试事件...');
            
            // 模拟在iframe中的选择事件
            const testMessage = {
                type: 'iframe-selection',
                text: '测试文字选择',
                rect: { top: 100, left: 200, width: 150, height: 20 },
                cumulativeOffset: { top: 50, left: 100 },
                iframePath: [0, 1]
            };
            
            window.postMessage(testMessage, '*');
            
            setTimeout(() => {
                // 模拟右键菜单事件
                const contextMessage = {
                    type: 'iframe-contextmenu',
                    clientX: 300,
                    clientY: 150,
                    cumulativeOffset: { top: 50, left: 100 },
                    iframePath: [0]
                };
                
                window.postMessage(contextMessage, '*');
            }, 1000);
            
        }, 2000);
    }
    
    // 初始化测试
    function initTests() {
        createTestPanel();
        testMessageListening();
        testIframeStructure();
        simulateTests();
        
        addTestResult('测试初始化', 'PASS', '所有测试已启动');
    }
    
    // 添加快捷键
    document.addEventListener('keydown', (e) => {
        if (e.ctrlKey && e.shiftKey && e.key === 'T') {
            e.preventDefault();
            const existingPanel = document.getElementById('iframe-test-panel');
            if (existingPanel) {
                existingPanel.remove();
            } else {
                initTests();
            }
        }
    });
    
    // 导出到全局
    window.iframeTestTool = {
        init: initTests,
        addResult: addTestResult,
        getResults: () => testResults
    };
    
    console.log('🧪 iframe测试工具已加载 - 按 Ctrl+Shift+T 开始测试');
    
})();
