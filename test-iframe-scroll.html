<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>iframe滚动条测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f0f0f0;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        .iframe-container {
            border: 2px solid #333;
            margin: 20px 0;
            padding: 10px;
            background-color: white;
        }
        .iframe-container h3 {
            margin-top: 0;
            color: #333;
        }
        iframe {
            width: 100%;
            height: 300px;
            border: 1px solid #ccc;
        }
        .test-content {
            padding: 20px;
            background-color: #e8f4f8;
            margin: 10px 0;
            border-radius: 5px;
        }
        .selectable-text {
            background-color: #fff3cd;
            padding: 10px;
            margin: 10px 0;
            border-left: 4px solid #ffc107;
            line-height: 1.6;
            position: relative;
        }
        .position-marker {
            position: absolute;
            top: 0;
            right: 0;
            background: #dc3545;
            color: white;
            padding: 2px 6px;
            font-size: 10px;
            border-radius: 0 0 0 4px;
        }
        .scroll-indicator {
            position: fixed;
            top: 50%;
            right: 20px;
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 10px;
            border-radius: 5px;
            font-size: 12px;
            z-index: 1000;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>iframe滚动条测试页面</h1>
        <p>这个页面专门测试iframe内部有滚动条时的划词和便签创建功能。</p>
        
        <div class="test-content">
            <h2>主页面内容</h2>
            <div class="selectable-text">
                <div class="position-marker">主页面</div>
                这是主页面的可选择文本。请尝试选择这段文字来测试划词功能。
                主页面没有滚动条，作为对比测试。
            </div>
        </div>

        <div class="iframe-container">
            <h3>有滚动条的iframe</h3>
            <iframe src="data:text/html;charset=utf-8,
                <!DOCTYPE html>
                <html>
                <head>
                    <meta charset='UTF-8'>
                    <style>
                        body { 
                            font-family: Arial, sans-serif; 
                            padding: 20px; 
                            background-color: #f8f9fa;
                            /* 设置固定高度，强制出现滚动条 */
                            height: 800px;
                            margin: 0;
                        }
                        .content { 
                            background-color: #d4edda; 
                            padding: 15px; 
                            margin: 10px 0; 
                            border-radius: 5px; 
                        }
                        .selectable { 
                            background-color: #fff3cd; 
                            padding: 10px; 
                            margin: 10px 0; 
                            border-left: 4px solid #ffc107; 
                            position: relative; 
                        }
                        .position-marker { 
                            position: absolute; 
                            top: 0; 
                            right: 0; 
                            background: #dc3545; 
                            color: white; 
                            padding: 2px 6px; 
                            font-size: 10px; 
                            border-radius: 0 0 0 4px; 
                        }
                        .spacer {
                            height: 100px;
                            background: linear-gradient(45deg, #e9ecef, #dee2e6);
                            margin: 20px 0;
                            display: flex;
                            align-items: center;
                            justify-content: center;
                            color: #6c757d;
                            font-weight: bold;
                        }
                        .test-area {
                            background-color: #ffeaa7;
                            padding: 20px;
                            margin: 20px 0;
                            border-radius: 8px;
                            border: 2px dashed #fdcb6e;
                        }
                    </style>
                </head>
                <body>
                    <h2>有滚动条的iframe内容</h2>
                    <div class='content'>
                        <p>这个iframe有垂直滚动条。请向下滚动测试不同位置的划词功能。</p>
                        <div class='selectable'>
                            <div class='position-marker'>顶部区域</div>
                            请选择这段文字测试iframe顶部区域的划词功能。
                            这里是iframe的顶部，滚动位置为0。
                        </div>
                    </div>
                    
                    <div class='spacer'>向下滚动继续测试</div>
                    <div class='spacer'>↓ 滚动测试区域 ↓</div>
                    
                    <div class='test-area'>
                        <h3>中间测试区域</h3>
                        <div class='selectable'>
                            <div class='position-marker'>中间区域</div>
                            请选择这段文字测试iframe中间区域的划词功能。
                            这里需要滚动才能看到，测试滚动偏移的计算是否正确。
                            右键点击这里测试便签创建位置是否准确。
                        </div>
                    </div>
                    
                    <div class='spacer'>继续向下滚动</div>
                    <div class='spacer'>↓ 更多测试内容 ↓</div>
                    
                    <div class='test-area'>
                        <h3>底部测试区域</h3>
                        <div class='selectable'>
                            <div class='position-marker'>底部区域</div>
                            请选择这段文字测试iframe底部区域的划词功能。
                            这里是iframe的底部区域，滚动位置最大。
                            右键点击测试便签创建位置的准确性。
                        </div>
                    </div>
                    
                    <div class='spacer'>测试完成</div>
                    
                    <script>
                        // 显示当前滚动位置
                        function updateScrollInfo() {
                            const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
                            const maxScroll = document.documentElement.scrollHeight - window.innerHeight;
                            console.log('iframe滚动位置:', scrollTop, '最大滚动:', maxScroll);
                        }
                        
                        window.addEventListener('scroll', updateScrollInfo);
                        updateScrollInfo();
                        
                        // 添加滚动位置指示器
                        const indicator = document.createElement('div');
                        indicator.style.cssText = \`
                            position: fixed;
                            top: 10px;
                            right: 10px;
                            background: rgba(0, 0, 0, 0.8);
                            color: white;
                            padding: 8px;
                            border-radius: 4px;
                            font-size: 12px;
                            z-index: 1000;
                        \`;
                        document.body.appendChild(indicator);
                        
                        function updateIndicator() {
                            const scrollTop = Math.round(window.pageYOffset || document.documentElement.scrollTop);
                            indicator.textContent = \`滚动: \${scrollTop}px\`;
                        }
                        
                        window.addEventListener('scroll', updateIndicator);
                        updateIndicator();
                    </script>
                </body>
                </html>
            "></iframe>
        </div>

        <div class="iframe-container">
            <h3>嵌套iframe（都有滚动条）</h3>
            <iframe src="data:text/html;charset=utf-8,
                <!DOCTYPE html>
                <html>
                <head>
                    <meta charset='UTF-8'>
                    <style>
                        body { 
                            font-family: Arial, sans-serif; 
                            padding: 20px; 
                            background-color: #e3f2fd;
                            height: 600px;
                            margin: 0;
                        }
                        .content { 
                            background-color: #bbdefb; 
                            padding: 15px; 
                            margin: 10px 0; 
                            border-radius: 5px; 
                        }
                        .selectable { 
                            background-color: #fff3e0; 
                            padding: 10px; 
                            margin: 10px 0; 
                            border-left: 4px solid #ff9800; 
                            position: relative; 
                        }
                        .position-marker { 
                            position: absolute; 
                            top: 0; 
                            right: 0; 
                            background: #f44336; 
                            color: white; 
                            padding: 2px 6px; 
                            font-size: 10px; 
                            border-radius: 0 0 0 4px; 
                        }
                        iframe { 
                            width: 100%; 
                            height: 250px; 
                            border: 2px solid #2196f3; 
                            margin: 10px 0; 
                        }
                        .spacer {
                            height: 80px;
                            background: linear-gradient(45deg, #e1f5fe, #b3e5fc);
                            margin: 15px 0;
                            display: flex;
                            align-items: center;
                            justify-content: center;
                            color: #0277bd;
                            font-weight: bold;
                        }
                    </style>
                </head>
                <body>
                    <h2>第一层iframe（有滚动条）</h2>
                    <div class='content'>
                        <div class='selectable'>
                            <div class='position-marker'>第1层顶部</div>
                            第一层iframe的顶部内容，请测试划词功能。
                        </div>
                    </div>
                    
                    <div class='spacer'>第一层滚动区域</div>
                    
                    <h3>嵌套的第二层iframe</h3>
                    <iframe src='data:text/html;charset=utf-8,
                        <!DOCTYPE html>
                        <html>
                        <head>
                            <meta charset=\"UTF-8\">
                            <style>
                                body { 
                                    font-family: Arial, sans-serif; 
                                    padding: 15px; 
                                    background-color: #f3e5f5;
                                    height: 500px;
                                    margin: 0;
                                }
                                .content { 
                                    background-color: #e1bee7; 
                                    padding: 10px; 
                                    margin: 10px 0; 
                                    border-radius: 5px; 
                                }
                                .selectable { 
                                    background-color: #fff8e1; 
                                    padding: 10px; 
                                    margin: 10px 0; 
                                    border-left: 4px solid #ffc107; 
                                    position: relative; 
                                }
                                .position-marker { 
                                    position: absolute; 
                                    top: 0; 
                                    right: 0; 
                                    background: #9c27b0; 
                                    color: white; 
                                    padding: 2px 6px; 
                                    font-size: 10px; 
                                    border-radius: 0 0 0 4px; 
                                }
                                .spacer {
                                    height: 60px;
                                    background: linear-gradient(45deg, #f8bbd9, #f48fb1);
                                    margin: 10px 0;
                                    display: flex;
                                    align-items: center;
                                    justify-content: center;
                                    color: #880e4f;
                                    font-weight: bold;
                                }
                            </style>
                        </head>
                        <body>
                            <h3>第二层iframe（有滚动条）</h3>
                            <div class=\"content\">
                                <div class=\"selectable\">
                                    <div class=\"position-marker\">第2层顶部</div>
                                    第二层iframe的顶部内容，测试嵌套滚动的划词功能。
                                </div>
                            </div>
                            
                            <div class=\"spacer\">第二层滚动区域</div>
                            <div class=\"spacer\">↓ 向下滚动测试 ↓</div>
                            
                            <div class=\"content\">
                                <div class=\"selectable\">
                                    <div class=\"position-marker\">第2层中部</div>
                                    第二层iframe的中部内容，需要滚动才能看到。
                                    请测试这里的划词和便签创建功能。
                                </div>
                            </div>
                            
                            <div class=\"spacer\">继续测试</div>
                            
                            <div class=\"content\">
                                <div class=\"selectable\">
                                    <div class=\"position-marker\">第2层底部</div>
                                    第二层iframe的底部内容，测试最大滚动位置的功能。
                                </div>
                            </div>
                        </body>
                        </html>
                    '></iframe>
                    
                    <div class='spacer'>第一层底部区域</div>
                    
                    <div class='content'>
                        <div class='selectable'>
                            <div class='position-marker'>第1层底部</div>
                            第一层iframe的底部内容，请测试划词功能。
                        </div>
                    </div>
                </body>
                </html>
            "></iframe>
        </div>

        <div class="test-content">
            <h2>测试说明</h2>
            <ul>
                <li><strong>滚动测试</strong>：在iframe内滚动到不同位置，测试划词工具栏位置是否准确</li>
                <li><strong>便签创建</strong>：在iframe的不同滚动位置右键创建便签，检查位置是否正确</li>
                <li><strong>嵌套滚动</strong>：在嵌套iframe中测试，每层都有独立的滚动条</li>
                <li><strong>坐标验证</strong>：使用调试工具查看坐标计算过程</li>
            </ul>
            
            <h3>预期效果</h3>
            <ul>
                <li>划词工具栏应该准确出现在选中文字的位置</li>
                <li>便签应该创建在右键点击的准确位置</li>
                <li>滚动不应该影响坐标计算的准确性</li>
                <li>多层嵌套时每层的滚动都应该被正确计算</li>
            </ul>
        </div>
    </div>

    <div class="scroll-indicator" id="main-scroll-indicator">
        主页面滚动: 0px
    </div>

    <script src="coordinate-debug-tool.js"></script>
    <script src="test-iframe-fixes.js"></script>
    <script>
        // 监听主页面滚动
        function updateMainScrollIndicator() {
            const scrollTop = Math.round(window.pageYOffset || document.documentElement.scrollTop);
            document.getElementById('main-scroll-indicator').textContent = `主页面滚动: ${scrollTop}px`;
        }
        
        window.addEventListener('scroll', updateMainScrollIndicator);
        updateMainScrollIndicator();
        
        // 监听消息，用于调试
        window.addEventListener('message', (e) => {
            if (e.data.type === 'iframe-selection' || e.data.type === 'iframe-contextmenu') {
                console.log('收到iframe消息:', e.data);
            }
        });
        
        // 添加调试说明
        const debugInfo = document.createElement('div');
        debugInfo.style.cssText = `
            position: fixed;
            bottom: 10px;
            left: 10px;
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 10px;
            border-radius: 5px;
            font-size: 12px;
            z-index: 1000;
            max-width: 300px;
        `;
        debugInfo.innerHTML = `
            <strong>滚动条测试工具:</strong><br>
            按 <kbd>Ctrl+Shift+D</kbd> 显示坐标调试面板<br>
            按 <kbd>Ctrl+Shift+T</kbd> 显示功能测试面板<br>
            在iframe中滚动并测试划词和便签创建
        `;
        document.body.appendChild(debugInfo);
    </script>
</body>
</html>
