import "@/assets/styles/content.less";
import "@/assets/styles/highlights.less";
import createSidePanelWrapper from "./sidepanel";
import { v4 as uuidv4 } from "uuid"; // 导入 v4 函数
import { createNote } from "@/utils/notes";
import FormFillingWrapper from "./formFilling";
import FormDetectorWrapper from "./FormDetectorWrapper";

import ReactDOM from "react-dom/client";

export default defineContentScript({
  matches: ["<all_urls>"],
  runAt: "document_end",
  main() {
    /*
     * 注入页面的JS逻辑
     * 开发者注意：
     * 1.该作用域没有this
     * 2.该作用域存在window和document对象，就是主页面对应的window和document对象
     */
    ////////// 以下是作用域全局参数
    const style = document.createElement("style");
    style.innerHTML = `
      .hypothesis-highlight {
        background-color: rgba(156, 230, 255, 0.5);
        &.is-transparent {
          background-color: rgb(208, 218, 121) !important;
          color: inherit !important;
        }
        &.hypothesis-highlight-focused {
          background-color: #ffec3d;
        }
      }
    `;
    document.head.appendChild(style);

    // 便签鼠标右键的位置
    let noteMouseX: number;
    let noteMouseY: number;
    /**
     * 监听鼠标右键，记录创建便签位置
     */
    document.addEventListener("contextmenu", function (event) {
      noteMouseX = event.clientX;
      // 鼠标右键点击位置相对于整个页面高度（包括滚动部分）的位置
      const scrollTop = document.documentElement.scrollTop || document.body.scrollTop;
      noteMouseY = scrollTop + event.clientY;
    });

    // 监听来自iframe的右键菜单消息，用于记录iframe中的便签创建位置
    window.addEventListener("message", (e: MessageEvent) => {
      if (e.data.type === "iframe-contextmenu") {
        const { clientX, clientY, cumulativeOffset } = e.data;

        if (cumulativeOffset) {
          // 使用累积偏移计算全局坐标
          noteMouseX = clientX + cumulativeOffset.left;
          noteMouseY = clientY + cumulativeOffset.top;
        } else {
          // 回退到基本处理
          noteMouseX = clientX;
          noteMouseY = clientY;
        }

        console.log("收到iframe右键菜单消息，更新便签创建位置:", { noteMouseX, noteMouseY });
      }
    });

    sessionStorage.setItem("sino-tap-key", uuidv4());

    // 只在顶级注册iframe消息监听
    if (window.top === window) {
      let guestInstance = null;

      // 设置全局的Guest实例引用，供iframe消息使用
      (window as any).setGuestInstance = (guest: any) => {
        guestInstance = guest;
      };

      // 确保Guest实例尽早创建的函数
      const ensureGuestInstance = async () => {
        if (!guestInstance) {
          // 动态导入createStrokeService函数
          const { createStrokeService } = await import("./annotator");
          guestInstance = createStrokeService([]);
        }
        return guestInstance;
      };

      // iframe 上报的划词
      window.addEventListener("message", async (e: MessageEvent) => {
        if (e.data.type === "iframe-selection") {
          const { rect, text, iframePath, cumulativeOffset } = e.data;

          await ensureGuestInstance();

          let globalRect;

          // 如果有累积偏移信息，直接使用（这是最准确的方式）
          if (cumulativeOffset) {
            globalRect = new DOMRect(
              rect.left + cumulativeOffset.left,
              rect.top + cumulativeOffset.top,
              rect.width,
              rect.height,
            );
          } else {
            // 兼容旧版本：尝试找到直接的iframe元素
            const iframeEl = getIframeByWindow(e.source as Window);
            if (!iframeEl) {
              // 如果找不到直接iframe，尝试通过路径查找多层嵌套的iframe
              const targetIframe = findNestedIframe(iframePath);
              if (!targetIframe) return;

              const iframeRect = targetIframe.getBoundingClientRect();
              globalRect = new DOMRect(rect.left + iframeRect.left, rect.top + iframeRect.top, rect.width, rect.height);
            } else {
              const iframeRect = iframeEl.getBoundingClientRect();
              globalRect = new DOMRect(rect.left + iframeRect.left, rect.top + iframeRect.top, rect.width, rect.height);
            }
          }

          browser.storage.local.set({ selectedText: text });

          // 标记这是来自iframe的划词，并传递文本和坐标信息
          (guestInstance as any)._isIframeSelection = true;
          (guestInstance as any)._iframeSelectedText = text;
          (guestInstance as any)._iframeGlobalRect = globalRect;

          (guestInstance as any)._adder.show(globalRect, false);
        }
        if (e.data.type === "iframe-mousedown" && guestInstance) {
          guestInstance._adder.hide();
        }
      });

      function getIframeByWindow(source: Window): HTMLIFrameElement | null {
        const iframes = document.querySelectorAll("iframe");
        for (const iframe of Array.from(iframes)) {
          if (iframe.contentWindow === source) {
            return iframe;
          }
        }
        return null;
      }

      // 通过路径查找嵌套的iframe
      function findNestedIframe(path: number[]): HTMLIFrameElement | null {
        if (!path || path.length === 0) return null;

        let currentDoc = document;
        let currentIframe: HTMLIFrameElement | null = null;

        for (const index of path) {
          const iframes = currentDoc.querySelectorAll("iframe");
          if (index >= iframes.length) return null;

          currentIframe = iframes[index] as HTMLIFrameElement;
          try {
            currentDoc = currentIframe.contentDocument || currentIframe.contentWindow?.document;
            if (!currentDoc) return null;
          } catch (e) {
            // 跨域限制，无法继续深入
            console.warn("无法访问嵌套iframe内容，可能存在跨域限制:", e);
            return currentIframe; // 返回能访问到的最深层iframe
          }
        }

        return currentIframe;
      }
    }

    /** 注册window加载完成事件 */
    window.addEventListener("load", async () => {
      // 组件直接注入网页
      createSidePanelWrapper();

      setInterval(() => {
        browser.runtime.sendMessage({ type: "wakeUp" });
      }, 6000000);
    });
    window.addEventListener("message", (event) => {
      // 只处理从网页发来的消息
      if (event.source !== window) return;
      if (event.data?.type === "chatKnowInfo") {
        browser.runtime.sendMessage({ type: "chatKnowInfo", data: event.data });
      }
    });
    /** 注册高亮消息监听时间 */
    browser.runtime.onMessage.addListener(async (message) => {
      switch (message.type) {
        case "createNote":
          createNote(message, noteMouseX, noteMouseY);
          break;
        default:
          break;
      }
    });
    // 表单检测面板注入
    const formDetectorShadowDom = document.createElement("shadow-dom");
    formDetectorShadowDom.id = "shadow-form-detector";
    const formDetectorShadowRoot = formDetectorShadowDom.attachShadow({ mode: "open" });

    // 导入FormDetector的CSS样式到Shadow DOM
    const formDetectorCSS = document.createElement("link");
    formDetectorCSS.rel = "stylesheet";
    formDetectorCSS.href = chrome.runtime.getURL("content-scripts/content.css");

    // 将CSS文件添加到Shadow DOM中
    formDetectorShadowRoot.appendChild(formDetectorCSS);

    // 只保留必要的容器和定位样式
    const formDetectorInlineStyle = document.createElement("style");
    formDetectorInlineStyle.textContent = `
      
      #form-detector-container {
        position: fixed;
        top: 0;
        left: 0;
        width: 100vw;
        height: 100vh;
        pointer-events: none;
        z-index: 10000;
      }
      
      #form-detector-container .form-detector-panel {
        pointer-events: auto;
       
      }
      #form-detector-container .data-collector-panel {
        pointer-events: auto;
       
      }
    `;

    formDetectorShadowRoot.appendChild(formDetectorInlineStyle);
    const formDetectorContainer = document.createElement("div");
    formDetectorContainer.id = "form-detector-container";
    formDetectorShadowRoot.appendChild(formDetectorContainer);

    ReactDOM.createRoot(formDetectorContainer).render(
      <FormDetectorWrapper formDetectorShadowRoot={formDetectorShadowRoot} />,
    );
    document.body.appendChild(formDetectorShadowDom);

    // 表单填充注入
    // const shadowDom = document.createElement("shadow-dom");
    // shadowDom.id = "shadow-side-filling-form";
    // const shadowRoot = shadowDom.attachShadow({ mode: "open" });
    // const fillingStyle = document.createElement("style");
    // 注入样式
    // fillingStyle.textContent = `
    //   #suggestion-container {
    //     position: absolute;
    //     top: 0;
    //     left: 0;
    //     z-index: 2147483647;
    //     font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Arial, sans-serif;
    //     font-size: 14px;
    //   }

    //   .suggestion-card {
    //     pointer-events: auto;
    //     width: 100%;
    //     background: white;
    //     border-radius: 8px;
    //     box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
    //     overflow: hidden;
    //   }

    //   .card-header {
    //     padding: 10px 15px;
    //     background: #f5f5f5;
    //     border-bottom: 1px solid #ddd;
    //     font-weight: bold;
    //     display: flex;
    //     justify-content: space-between;
    //     align-items: center;
    //   }

    //   .close-button {
    //     background: none;
    //     border: none;
    //     cursor: pointer;
    //     font-size: 16px;
    //     color: #777;
    //   }

    //   .close-button:hover {
    //     color: #333;
    //   }

    //   .suggestions-list {
    //     max-height: 300px;
    //     overflow-y: auto;
    //     padding: 5px 0;
    //   }

    //   .suggestion-item {
    //     padding: 5px 15px;
    //     display: flex;
    //     justify-content: space-between;
    //     align-items: center;
    //     cursor: pointer;
    //   }
    //   .suggestion-item:hover {
    //     background-color: #F8F9FA;
    //   }

    //   .suggestion-content {
    //     flex: 1;
    //     white-space: nowrap;
    //     overflow: hidden;
    //     text-overflow: ellipsis;
    //   }
    //   .loading-item {
    //     padding: 10px 15px;
    //     display: flex;
    //     justify-content: space-between;
    //     align-items: center;
    //   }

    //   .use-button {
    //     padding: 5px 10px;
    //     background: #4285f4;
    //     color: white;
    //     border: none;
    //     border-radius: 4px;
    //     cursor: pointer;
    //     margin-left: 10px;
    //   }

    //   .use-button:hover {
    //     background: #3367d6;
    //   }
    // `;
    // shadowRoot.appendChild(fillingStyle);
    // // 注入dom
    // const shadowContainer = document.createElement("div");
    // shadowContainer.id = "filling-form-container";
    // shadowRoot.appendChild(shadowContainer);
    // ReactDOM.createRoot(shadowContainer).render(<FormFillingWrapper />);
    // document.body.parentNode.appendChild(shadowDom);
  },
});
