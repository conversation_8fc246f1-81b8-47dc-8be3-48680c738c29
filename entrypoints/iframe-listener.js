import { defineContentScript } from "wxt/sandbox";

export default defineContentScript({
  matches: ["<all_urls>"],
  runAt: "document_idle",
  allFrames: true, // 让它能注入到 iframe
  main(ctx) {
    if (window.top !== window) {
      // document.addEventListener("mouseup", () => {
      //   console.log("22333333")
      //   const selection = window.getSelection();
      //   if (!selection || selection.isCollapsed) return;

      //   const range = selection.getRangeAt(0);
      //   const rect = range.getBoundingClientRect();

      //   window.top?.postMessage(
      //     {
      //       type: "iframe-selection",
      //       text: selection.toString(),
      //       rect: {
      //         top: rect.top,
      //         left: rect.left,
      //         width: rect.width,
      //         height: rect.height,
      //       },
      //     },
      //     "*",
      //   );
      // });
      // document.addEventListener("mouseup", (e) => {
      //   const target = e.target;
      //   if (target && (target.tagName === "INPUT" || target.tagName === "TEXTAREA")) {
      //     const el = target
      //     const start = el.selectionStart ?? 0;
      //     const end = el.selectionEnd ?? 0;
      //     const text = el.value.substring(start, end);
      //     console.log(text, 1231)
      //     if (text) {
      //       const rect = el.getBoundingClientRect();
      //       window.top?.postMessage(
      //         {
      //           type: "iframe-input-selection",
      //           text,
      //           rect: {
      //             top: rect.top,
      //             left: rect.left,
      //             width: rect.width,
      //             height: rect.height,
      //           },
      //         },
      //         "*",
      //       );
      //     }
      //   }
      // });

      // 计算当前iframe在整个嵌套结构中的路径和累积偏移
      function getIframePathAndOffset() {
        const path = [];
        let currentWindow = window;
        let cumulativeOffset = { top: 0, left: 0 };

        // 从当前窗口向上遍历到顶层
        while (currentWindow !== window.top && currentWindow.parent !== currentWindow) {
          try {
            // 在父窗口中找到当前窗口对应的iframe元素
            const parentDoc = currentWindow.parent.document;
            const iframes = parentDoc.querySelectorAll('iframe');

            for (let i = 0; i < iframes.length; i++) {
              if (iframes[i].contentWindow === currentWindow) {
                const rect = iframes[i].getBoundingClientRect();
                const parentScrollTop = currentWindow.parent.pageYOffset || parentDoc.documentElement.scrollTop;
                const parentScrollLeft = currentWindow.parent.pageXOffset || parentDoc.documentElement.scrollLeft;

                cumulativeOffset.top += rect.top + parentScrollTop;
                cumulativeOffset.left += rect.left + parentScrollLeft;
                path.unshift(i); // 添加到路径开头
                break;
              }
            }
          } catch (e) {
            // 跨域访问限制，无法获取精确偏移，使用估算值
            console.warn('无法访问父窗口，可能存在跨域限制:', e);
            break;
          }

          currentWindow = currentWindow.parent;
        }

        return { path, offset: cumulativeOffset };
      }

      // 监听来自子iframe的消息并转发
      window.addEventListener("message", (e) => {
        // 只处理来自子iframe的消息
        if (e.source && e.source !== window && (e.data.type === "iframe-selection" || e.data.type === "iframe-mousedown")) {
          // 如果消息包含坐标信息，需要加上当前iframe的偏移
          if (e.data.type === "iframe-selection" && e.data.rect) {
            try {
              // 找到发送消息的子iframe
              const childIframes = document.querySelectorAll('iframe');
              for (const iframe of childIframes) {
                if (iframe.contentWindow === e.source) {
                  const iframeRect = iframe.getBoundingClientRect();
                  const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
                  const scrollLeft = window.pageXOffset || document.documentElement.scrollLeft;

                  // 更新坐标，加上当前iframe的偏移
                  e.data.rect = {
                    top: e.data.rect.top + iframeRect.top + scrollTop,
                    left: e.data.rect.left + iframeRect.left + scrollLeft,
                    width: e.data.rect.width,
                    height: e.data.rect.height,
                  };
                  break;
                }
              }
            } catch (error) {
              console.warn('计算iframe偏移时出错:', error);
            }
          }

          // 继续向上传递消息
          if (window.parent !== window) {
            window.parent.postMessage(e.data, "*");
          }
        }
      });

      document.addEventListener("pointerup", () => {
        setTimeout(() => {
          const selection = window.getSelection();
          if (!selection || selection.isCollapsed) return;

          const text = selection.toString().trim();
          if (!text) return;

          const range = selection.getRangeAt(0);
          const rect = range.getBoundingClientRect();

          // 获取当前iframe的路径和累积偏移
          const { path, offset } = getIframePathAndOffset();
          const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
          const scrollLeft = window.pageXOffset || document.documentElement.scrollLeft;

          const messageData = {
            type: "iframe-selection",
            text,
            rect: {
              top: rect.top + scrollTop,
              left: rect.left + scrollLeft,
              width: rect.width,
              height: rect.height,
            },
            iframePath: path, // 添加iframe路径信息
            cumulativeOffset: offset, // 添加累积偏移信息
          };

          // 向父级发送消息（会被逐层转发到顶层）
          if (window.parent !== window) {
            window.parent.postMessage(messageData, "*");
          }
        }, 10); // 微延迟确保 Range 已更新
      });

      document.addEventListener("pointerdown", (e) => {
        // 点击任何地方都通知上级
        const messageData = { type: "iframe-mousedown" };
        if (window.parent !== window) {
          window.parent.postMessage(messageData, "*");
        }
      });

    }
  },
});
