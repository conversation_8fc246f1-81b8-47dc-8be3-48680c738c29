// 调试工具：帮助分析iframe嵌套结构和消息传递
(function() {
    'use strict';
    
    // 创建调试面板
    function createDebugPanel() {
        const panel = document.createElement('div');
        panel.id = 'iframe-debug-panel';
        panel.style.cssText = `
            position: fixed;
            top: 10px;
            right: 10px;
            width: 300px;
            max-height: 400px;
            background: rgba(0, 0, 0, 0.9);
            color: white;
            padding: 10px;
            border-radius: 5px;
            font-family: monospace;
            font-size: 12px;
            z-index: 2147483647;
            overflow-y: auto;
        `;
        
        const title = document.createElement('h3');
        title.textContent = 'iframe调试信息';
        title.style.margin = '0 0 10px 0';
        panel.appendChild(title);
        
        const content = document.createElement('div');
        content.id = 'debug-content';
        panel.appendChild(content);
        
        document.body.appendChild(panel);
        return content;
    }
    
    // 分析iframe结构
    function analyzeIframeStructure() {
        const results = [];
        
        function analyzeLevel(doc, level = 0, path = []) {
            const iframes = doc.querySelectorAll('iframe');
            
            results.push({
                level,
                path: [...path],
                iframeCount: iframes.length,
                isTop: doc === document,
                url: doc.location?.href || 'unknown'
            });
            
            iframes.forEach((iframe, index) => {
                try {
                    const iframeDoc = iframe.contentDocument || iframe.contentWindow?.document;
                    if (iframeDoc) {
                        analyzeLevel(iframeDoc, level + 1, [...path, index]);
                    }
                } catch (e) {
                    results.push({
                        level: level + 1,
                        path: [...path, index],
                        iframeCount: 0,
                        isTop: false,
                        url: 'cross-origin',
                        error: e.message
                    });
                }
            });
        }
        
        analyzeLevel(document);
        return results;
    }
    
    // 更新调试信息
    function updateDebugInfo() {
        const content = document.getElementById('debug-content');
        if (!content) return;
        
        const structure = analyzeIframeStructure();
        
        let html = '<div><strong>iframe结构分析:</strong></div>';
        structure.forEach(item => {
            const indent = '  '.repeat(item.level);
            html += `<div>${indent}Level ${item.level}: ${item.iframeCount} iframes</div>`;
            html += `<div>${indent}Path: [${item.path.join(', ')}]</div>`;
            if (item.error) {
                html += `<div style="color: #ff6b6b">${indent}Error: ${item.error}</div>`;
            }
            html += '<div>---</div>';
        });
        
        html += '<div><strong>消息监听状态:</strong></div>';
        html += `<div>当前窗口: ${window === window.top ? 'TOP' : 'IFRAME'}</div>`;
        html += `<div>父窗口: ${window.parent === window ? 'NONE' : 'EXISTS'}</div>`;
        
        content.innerHTML = html;
    }
    
    // 监听消息并记录
    const messageLog = [];
    window.addEventListener('message', (e) => {
        const logEntry = {
            timestamp: new Date().toLocaleTimeString(),
            type: e.data?.type || 'unknown',
            source: e.source === window.top ? 'TOP' : e.source === window.parent ? 'PARENT' : 'OTHER',
            data: e.data
        };
        
        messageLog.push(logEntry);
        if (messageLog.length > 10) {
            messageLog.shift(); // 保持最新10条记录
        }
        
        console.log('iframe消息:', logEntry);
        updateMessageLog();
    });
    
    // 更新消息日志
    function updateMessageLog() {
        const content = document.getElementById('debug-content');
        if (!content) return;
        
        let html = content.innerHTML;
        html += '<div><strong>最近消息:</strong></div>';
        
        messageLog.slice(-5).forEach(log => {
            html += `<div style="color: #4ecdc4">${log.timestamp} - ${log.type}</div>`;
        });
        
        content.innerHTML = html;
    }
    
    // 测试消息发送
    function testMessageSending() {
        const testMessage = {
            type: 'debug-test',
            timestamp: Date.now(),
            from: window === window.top ? 'top' : 'iframe'
        };
        
        if (window.parent !== window) {
            window.parent.postMessage(testMessage, '*');
            console.log('发送测试消息到父窗口:', testMessage);
        }
        
        if (window !== window.top) {
            window.top.postMessage(testMessage, '*');
            console.log('发送测试消息到顶层窗口:', testMessage);
        }
    }
    
    // 初始化
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', () => {
            setTimeout(() => {
                createDebugPanel();
                updateDebugInfo();
                
                // 添加测试按钮
                const button = document.createElement('button');
                button.textContent = '测试消息';
                button.style.cssText = 'margin: 5px 0; padding: 5px; background: #4ecdc4; border: none; color: white; border-radius: 3px; cursor: pointer;';
                button.onclick = testMessageSending;
                document.getElementById('iframe-debug-panel').appendChild(button);
            }, 1000);
        });
    } else {
        setTimeout(() => {
            createDebugPanel();
            updateDebugInfo();
            
            // 添加测试按钮
            const button = document.createElement('button');
            button.textContent = '测试消息';
            button.style.cssText = 'margin: 5px 0; padding: 5px; background: #4ecdc4; border: none; color: white; border-radius: 3px; cursor: pointer;';
            button.onclick = testMessageSending;
            document.getElementById('iframe-debug-panel').appendChild(button);
        }, 1000);
    }
    
    // 导出到全局作用域供调试使用
    window.iframeDebugHelper = {
        analyzeStructure: analyzeIframeStructure,
        updateInfo: updateDebugInfo,
        testMessage: testMessageSending,
        getMessageLog: () => messageLog
    };
    
})();
